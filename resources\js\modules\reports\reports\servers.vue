<template>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="
                itemData?.name_ar ??
                $t(router.currentRoute.value.meta.breadcrumb)
            "
            :reset="parent ? false : true"
        >
        </t-breadcrumbs>
    </v-container>
        <v-btn @click="reloadData" icon="mdi-refresh" class="float-end"></v-btn>

    <v-container>
        <h3>{{ $t("reports.servers.servers") }}</h3>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
                :rows="tableData"
                :loading="isLoading"
                :query="query"
                :userPermissions="userPermissions"
                :cols="serverCols"
                @loadData="getServers"
            >
            </t-data-table>
        </div>
    </v-container>
</template>

<script setup>
import TDataTable from "@/shared/components/t-data-table.vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import useServers from "@/modules/reports/composables/servers";
import { onMounted } from "vue";

const {
    parent,
    tableData,
    pagination,
    query,
    isLoading,
    updateModal,
    storeModal,
    cancel,
    itemData,
    serverCols,
    updateModalItem,
    loadParentData,
    loadData,
    router,
    userPermissions,
    valid,
    validation,
    form,
    service,
    server,
    getItem,
    getServers,
    reloadData,
} = useServers();
</script>
