<?php

namespace App\Services;

use App\Enums\ExpenseTypesEnum;
use App\Enums\ServerResources as ServerResourcesEnum;
use App\Http\Requests\ServerRequests\StoreServerRequest;
use App\Http\Requests\ServerRequests\UpdateServerRequest;
use App\Models\Server;
use App\Models\Setting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ServerService
{

    public function requestExceptions()
    {
        return array_merge(
            ServerResourcesEnum::getVAlues(),
            array_map(function($value) {
                return $value . '_threshold';
            }, ServerResourcesEnum::getVAlues())
        );
    }

    public function resources($request)
    {

        $resourceDefaultThreshold = Setting::whereIn('code', ServerResourcesEnum::getDataForAttr('settings_attr'))->get()->keyBy('code');
        $serverResources = collect(ServerResourcesEnum::getData())->map(function ($resource) use ($request, $resourceDefaultThreshold) {
            return [
                'resource' => $resource['value'],
                'value' => $request->validated($resource['value']),
                'threshold' => $request->validated($resource['value'] . '_threshold') ??
                 $resourceDefaultThreshold[$resource['settings_attr']]['value']
            ];
        })->toArray();

        return $serverResources;
    }

    public function store(StoreServerRequest $request)
    {
        DB::beginTransaction();
        try {
            $server = Server::create($request->safe()->except($this->requestExceptions()));

            /* create server resources */
            $ServerResources = collect($this->resources($request))->keyBy('resource');
            $server->resources()->createMany($ServerResources);

            /* create a new server expense */
            $server->updateOrCreateExpense();

            /* Tokenize the server */
            $server->tokenize();

            DB::commit();
            return $server;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function update(UpdateServerRequest $request, Server $server)
    {
        DB::beginTransaction();
        try {
            $server->update($request->safe()->except($this->requestExceptions()));
            /* create server resources */

            $newServerResources = collect($this->resources($request))->keyBy('resource');
            $server->resources()->each(
                function ($resource) use ($newServerResources) {
                    $resource->update($newServerResources[$resource->resource]);
                }
            );

            /* update the server expense */
            $server->updateOrCreateExpense();

            DB::commit();
            return $server;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function delete($server)
    {
        DB::beginTransaction();
        try {
            $server->delete();
            $server->resources()->delete();
            $server->ports()->delete();
            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }

    }
}
