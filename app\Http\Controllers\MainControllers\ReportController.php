<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\ServerInternalStatus;
use App\Http\Controllers\Controller;
use App\Models\ClientNotification;
use App\Models\Server;
use Illuminate\Http\Request;

class ReportController extends Controller
{
    public function getServers()
    {
        $data = [];
        Server::with('resources')
            ->withSum('tcpUsers', 'current_connections_count')
            ->withSum('udpUsers', 'current_connections_count')
            ->withSum('wireguardUsers', 'current_connections_count')
            ->get()
            ->map(function ($server) use (&$data) {
                $resourcesCollection = collect($server->resources)->keyBy('resource');
                $tcpUsers =  $server->tcp_users_sum_current_connections_count;
                $udpUsers = $server->udp_users_sum_current_connections_count;
                $wireguardUsers = $server->wireguard_users_sum_current_connections_count;

                $data[] = [
                    'name' => $server['name'],
                    'status' => $server['translated_internal_status'],
                    'cpu' => $resourcesCollection['cpu']['consumption'],
                    'ram' => $resourcesCollection['ram']['consumption'],
                    'disk' => $resourcesCollection['disk']['consumption'],
                    'tcp_users' => $tcpUsers,
                    'udp_users' => $udpUsers,
                    'wireguard_users' => $wireguardUsers,
                    'total_users' => $tcpUsers + $udpUsers + $wireguardUsers
                ];
            });
        return $data;
    }
}
