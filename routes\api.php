<?php

use App\Http\Controllers\Controller;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MainControllers\LocationController;
use App\Http\Controllers\MainControllers\AuthenticationController;
use App\Http\Controllers\MainControllers\ExpenseController;
use App\Http\Controllers\MainControllers\ClientController;
use App\Http\Controllers\MainControllers\GroupController;
use App\Http\Controllers\MainControllers\PermissionController;
use App\Http\Controllers\MainControllers\ProtocolController;
use App\Http\Controllers\MainControllers\RoleController;
use App\Http\Controllers\MainControllers\UsersController;
use App\Http\Controllers\MainControllers\ServerController;
use App\Http\Controllers\StatisticsController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use \App\Http\Controllers\MainControllers\ProviderController;
use \App\Http\Controllers\MainControllers\TicketController;
use \App\Http\Controllers\MainControllers\SettingController;
use \App\Http\Controllers\MainControllers\NotificationsController;
use \App\Http\Controllers\MainControllers\ClientNotificationController;
use App\Http\Controllers\MainControllers\PortController;
use \App\Http\Controllers\MainControllers\PackageController;
use App\Http\Controllers\MainControllers\ReportController;
use \App\Http\Controllers\MainControllers\SubscriptionController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

//Auth
Route::post('login', [AuthenticationController::class, 'login']);

Route::middleware('auth:sanctum')->group(function () {


    /* ==================== User ====================*/
    Route::post('logout', [AuthenticationController::class, 'logout']);
    Route::apiResource('/users', UsersController::class);
    Route::get('/users/toggle-activation/{user}', [UsersController::class, 'toggleActivation']);

    /* ==================== Location ====================*/
    Route::apiResource('/locations', LocationController::class);
    Route::get('get-auto-locations', [LocationController::class, 'getAutoLocations']);
    Route::get('get-auto-protocols', [LocationController::class, 'getAutoProtocols']);

    /* ==================== Permission ====================*/
    Route::get('/permissions', [PermissionController::class, 'index']);

    /* ==================== Role ====================*/
    Route::apiResource('/roles', RoleController::class);
    Route::post('/roles/assignPermissions/{role}', [RoleController::class, 'assignPermissions']);

    /* ==================== Server ====================*/
    Route::post('/servers/regenerate-token/{server}', [ServerController::class, 'regenerateToken']);
    Route::apiResource('/servers', ServerController::class);
    Route::get('/servers-providers', [ServerController::class, 'providers']);

    /* ==================== providers ====================*/
    Route::apiResource('/providers', ProviderController::class)->except(['show']);

    /* ==================== tickets ====================*/
    Route::apiResource('/tickets', TicketController::class)->only(['index', 'update','show']);

    /* ==================== Settings ====================*/
    Route::apiResource('/settings', SettingController::class)->only(['index', 'update','show']);
    Route::get('index-content', [SettingController::class, 'indexContent']);

    /* ==================== Protocols ====================*/
    Route::apiResource('/protocols', ProtocolController::class);

    /* ==================== clients ====================*/
    Route::apiResource('/clients', ClientController::class);

    /* ==================== devices ====================*/
    Route::post('clients-devices', [ClientController::class, 'devices']);

    /* ==================== sessions ====================*/
    Route::post('clients-sessions', [ClientController::class, 'sessions']);


    /* ==================== groups ====================*/
    Route::apiResource('groups', GroupController::class);
    Route::get('groups-clients', [GroupController::class, 'clients']);
    Route::get('groups-group-clients', [GroupController::class, 'groupClients']);
    Route::post('groups-detach-client/{group}', [GroupController::class, 'detachClient']);


    //Protocol
    /* ==================== Protocol ====================*/
    Route::apiResource('/protocols', ProtocolController::class)->except(['store', 'destroy']);

    /* ==================== Packages ====================*/
    Route::apiResource('/packages', PackageController::class);
    Route::get('/packages/disable/{package}', [PackageController::class, 'disable']);

    /* ==================== subscriptions ====================*/
    Route::apiResource('/subscriptions', SubscriptionController::class)->only(['index','store']);
    Route::get('get-packages', [SubscriptionController::class, 'getPackages']);
    Route::post('/subscriptions/disable/{subscription}', [SubscriptionController::class, 'disable']);

    /* ==================== expenses ====================*/
    Route::apiResource('/expenses', ExpenseController::class);
    Route::get('expenses-servers', [ExpenseController::class, 'servers']);




    /* ==================== Notifications ====================*/
    Route::apiResource('/notifications', NotificationsController::class);
    Route::get('get-groups', [NotificationsController::class, 'getGroups']);
    Route::get('notifications-clients', [NotificationsController::class, 'clients']);

    /* ==================== ClientNotifications ====================*/
    Route::apiResource('/client-notifications', ClientNotificationController::class)->only(['index']);

    /* ==================== ClientPorts ====================*/
    Route::apiResource('/ports', PortController::class);
    Route::get('ports-protocols', [PortController::class, 'protocols']);

    /* ==================== Reports ====================*/
    Route::prefix('reports')->group(function () {
        Route::get('servers', [ReportController::class, 'getServers']);
    });

    Route::get('dashboard/subscriptions', [DashboardController::class, 'subscriptions']);
    Route::get('dashboard/expenses', [DashboardController::class, 'expenses']);
    Route::get('dashboard/notifications', [DashboardController::class, 'notifications']);
    Route::get('dashboard/servers', [DashboardController::class, 'servers']);
    /* ==================== Statistics ====================*/
    Route::get('statistics-clients', [StatisticsController::class, 'clientsStatistics']);
});

Route::get('lang', [Controller::class, 'switchLang']);
