<template src="@/modules/protocols-management/protocols/templates/edit.html"></template>
<script setup lang="ts">
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import { onMounted } from "vue";
import { reactive, ref } from 'vue'
import ProtocolsService from "@/services/protocols-service.js";
import useShared from "@/helpers/shared.js";
import protocolTableItems from "../models/protocol-table-items";

const props = defineProps({
    id: {
        required: true,
        type: String,
    },
});


            const {
                validationRules,
                tableData,
                pagination,
                valid,
                query,
                isLoading,
                service,
                itemData,
                loadData,
                getItem,
                storeItem,
                updateItem,
                deleteItem,
                parent,
                router,
                errorHandle,
                userPermissions,
                t,
                redirect
            } = useShared()

            service.value = ProtocolsService;

            const {
                cols: protocolCols,
                actions: protocolActions
            } = protocolTableItems(t, deleteItem, redirect);


            const form = reactive({
                'name': null,
                'code': null,
                'file': null,
                'template': null
            });

            const validation = {
                name: [
                    validationRules.required,
                    validationRules.minLength(3),
                    validationRules.maxLength(50),
                ],
                code: [
                    validationRules.required,
                    validationRules.minLength(2),
                    validationRules.maxLength(50),
                ],
                file: [
                    validationRules.required
                ],
                template: [
                    validationRules.required
                ],
            }

        onMounted(() => {
            getItem(props.id, true);
        });

        const saveProtocol = async () => {
            await updateItem(itemData.value, "protocols", true);
        };


</script>
