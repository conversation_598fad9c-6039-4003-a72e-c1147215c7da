<?php

namespace App\Services\MobileServices;

use App\Enums\ClientTypesEnum;
use App\Helpers\MailHelper;
use App\Http\Requests\MobileRequests\DeleteMyAccountRequest;
use App\Http\Requests\MobileRequests\RegisterRequest;
use App\Http\Requests\MobileRequests\ResetPasswordRequest;
use App\Http\Requests\MobileRequests\ValidateResetCodeAndLoginRequest;
use App\Http\Requests\MobileRequests\VerifyEmailRequest;
use App\Mail\PasswordResetMail;
use App\Mail\VerificationCodeMail;
use App\Models\Client;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class AuthService
{

    public function sendVerificationCode(Client $client)
    {
        $verificationCode = random_int(100000, 999999);
        if (MailHelper::queue($client['email'], new VerificationCodeMail(($verificationCode))))
            return $client->update(["verification_code" => $verificationCode]);
        return false;
    }

    public function register(RegisterRequest $request)
    {
        DB::beginTransaction();
        try {
            /* Client Creation */
            $input = [
                'email' => $request->validated('email'),
                'password' => $request->validated('password'),
                'type' => ClientTypesEnum::FREE->value
            ];
            $client = Client::updateOrCreate(['email' => $input['email']], $input);
            $client->update(['username' => $client->id . Str::random(3)]);

            /* Send code to verfiy the email */
            $this->sendVerificationCode($client);

            DB::commit();
            return $client;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function verifyEmail(VerifyEmailRequest $request, Client $client)
    {
        DB::beginTransaction();
        try {
            if ($client->isVerified()) {
                throw ValidationException::withMessages(['email' => __('messages.your_email_has_been_verified_before')]);
            }
            if (!$client->matchVerificationCode($request->validated('verification_code'))) {
                throw ValidationException::withMessages(['verification_code' => __('messages.invalid_verification_code')]);
            }
            $client->markAsVerified();
            $res = $client->tokenize();
            DB::commit();
            return $res;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function sendResetPasswordCode(Client $client)
    {
        /* Send code to reset the email */
        $verificationCode = random_int(100000, 999999);
        if (MailHelper::queue($client['email'], new PasswordResetMail($verificationCode)))
            DB::table('password_reset_codes')
                ->where('email', $client['email'])
                ->updateOrInsert(['email' => $client['email']], ['code' => $verificationCode]);

        return $client;
    }

    public function validateResetCodeAndLogin(ValidateResetCodeAndLoginRequest $request, Client $client)
    {
        if (!$client->matchResetPasswordVerificationCode($request->validated('verification_code'))) {
            throw ValidationException::withMessages(['verification_code' => __('messages.invalid_verification_code')]);
        }
        
        /* login */
        $res = $client->tokenize();
        return $res;
    }

    public function resetPassword(ResetPasswordRequest $request, Client $client)
    {
        DB::beginTransaction();
        try {
            $client->changePassword($request->validated('password'));
            DB::table('password_reset_codes')
                ->where('email', $client['email'])
                ->delete();
            DB::commit();
            return true;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function deleteMyAccount(DeleteMyAccountRequest $request, Client $client)
    {
        if (!$client->matchWithCurrentPassword($request->validated('password')))
            throw ValidationException::withMessages(['password' => __('messages.invalid_password')]);

        $client->delete();
        return true;
    }

    public function changePassword(string $oldPassword, string $newPassword, Client $client)
    {
        if (!$client->matchWithCurrentPassword($oldPassword))
            throw ValidationException::withMessages(['old_password' => __('messages.invalid_password')]);

        $client->changePassword($newPassword);
        return true;
    }
}
