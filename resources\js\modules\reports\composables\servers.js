import {reactive, ref} from 'vue'
import useShared from "@/helpers/shared.js";
import serverTableItems from '../models/server-table-items';
import reportsService from '@/services/reports-service';
import { useIntervalFn } from '@vueuse/core'

export default function useServers() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        loadData,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        deleteItem,
        errorHandle,
        cancel,
        saveItem,
        router,
        userPermissions,
        t,
        cookie,
        redirect
    } = useShared()

    service.value = reportsService;

    const server = ref();

    const {
        cols: serverCols,
    } = serverTableItems(t, redirect, showUpdateModal);

    const getServers = async (showLoader = false) => {
        try {
            isLoading.value = true;
            const data = await service.value.getServers(showLoader);
            tableData.value = data.data
            isLoading.value = false
        } catch (error) {
            isLoading.value = false
            await errorHandle(error)
        }
    }

    const { pause, resume, isActive } = useIntervalFn(getServers, 20000);

    const resetInterval = () => {
        pause();
        resume();
    };

    const reloadData = () => {
        resetInterval();
        getServers(false);
    };

    return {
        itemData,
        tableData,
        pagination,
        query,
        server,
        isLoading,
        parent,
        updateModal,
        storeModal,
        getItem,
        loadData,
        service,
        loadParentData,
        storeItem,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        serverCols,
        getServers,
        reloadData,
    }
}
