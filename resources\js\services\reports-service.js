import axios from "axios";
import BaseService from "./base-service";
import authHeader from "./auth-header";

class ReportsService extends BaseService {
    routPath = '/reports';

    constructor() {
        super();
    }

    getServers(showLoader) {
        return axios.get(this.routPath + '/servers'
            , { headers: authHeader(), showLoader }
        );
    }

    getServersStatistics(showLoader) {
        return axios.get(this.routPath + '/servers/statistics'
            , { headers: authHeader(), showLoader }
        );
    }
}

export default new ReportsService();
